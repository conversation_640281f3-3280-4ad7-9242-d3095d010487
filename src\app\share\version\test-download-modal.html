<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下载弹窗测试</title>
    <style>
        /* 模拟 Ionic 模态框样式 */
        .modal-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        
        .dialog-box-xs {
            --height: auto;
            --max-height: 70vh;
            --min-height: 200px;
            --width: 280px;
            height: var(--height);
            max-height: var(--max-height);
            min-height: var(--min-height);
            width: var(--width);
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
        }
        
        /* 复制下载组件样式 */
        .download-container {
            display: flex;
            flex-direction: column;
            height: 100%;
            max-height: 70vh;
            min-height: 200px;
            overflow: hidden;
            box-sizing: border-box;
        }
        
        .download-header {
            text-align: center;
            min-height: 42px;
            height: 42px;
            line-height: 42px;
            width: 100%;
            color: white;
            background-color: #4285F4;
            font-size: 16px;
            font-weight: 500;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 16px;
            box-sizing: border-box;
        }
        
        .download-context {
            padding: 20px 16px;
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            flex: 1;
            min-height: 100px;
            background-color: #fff;
        }
        
        .download-context div {
            font-size: 14px;
            color: #333;
            margin-bottom: 12px;
            font-weight: 500;
            line-height: 1.4;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background-color: #e0e0e0;
            border-radius: 3px;
            overflow: hidden;
            margin-top: 8px;
        }
        
        .progress-fill {
            height: 100%;
            background-color: #4285F4;
            transition: width 0.3s ease;
            border-radius: 3px;
        }
        
        .download-footer {
            border-top: 1px solid #f6f6f6;
            min-height: 42px;
            height: 42px;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            background-color: #fff;
            flex-shrink: 0;
            padding: 0 16px;
            box-sizing: border-box;
        }
        
        .download-footer div {
            font-size: 14px;
            color: #666;
            font-weight: 500;
            line-height: 1.4;
            text-align: center;
        }
        
        .btn-install {
            width: 100%;
            text-align: center;
            color: #4285F4;
            font-size: 14px;
            font-weight: 500;
            padding: 12px 16px;
            cursor: pointer;
            border-radius: 4px;
            transition: background-color 0.2s;
            min-height: 42px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            background: transparent;
        }
        
        .btn-install:hover {
            background-color: rgba(66, 133, 244, 0.1);
        }
        
        .btn-install:active {
            background-color: rgba(66, 133, 244, 0.2);
        }
        
        .test-controls {
            position: fixed;
            top: 10px;
            left: 10px;
            z-index: 2000;
            background: white;
            padding: 10px;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .test-controls button {
            margin: 5px;
            padding: 8px 12px;
            border: 1px solid #ccc;
            border-radius: 4px;
            cursor: pointer;
        }
        
        /* 响应式适配测试 */
        @media (max-height: 600px) {
            .download-header {
                min-height: 38px;
                height: 38px;
                line-height: 38px;
                font-size: 15px;
            }
            
            .download-context {
                padding: 16px 12px;
                min-height: 80px;
            }
            
            .download-context div {
                font-size: 13px;
            }
        }
        
        @media (max-height: 500px) {
            .download-header {
                min-height: 36px;
                height: 36px;
                line-height: 36px;
                font-size: 14px;
            }
            
            .download-context {
                padding: 12px 8px;
                min-height: 60px;
            }
            
            .download-context div {
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="test-controls">
        <button onclick="showDownloading()">下载中状态</button>
        <button onclick="showCompleted()">下载完成状态</button>
        <button onclick="showError()">下载失败状态</button>
        <button onclick="hideModal()">关闭弹窗</button>
        <br>
        <button onclick="simulateProgress()">模拟下载进度</button>
    </div>
    
    <div id="modal" class="modal-backdrop" style="display: none;">
        <div class="dialog-box-xs">
            <div class="download-container">
                <div class="download-header">
                    版本更新中
                </div>
                <div class="download-context">
                    <div>
                        下载进度
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                    </div>
                </div>
                <div class="download-footer" id="footer">
                    <!-- 内容将通过JavaScript动态填充 -->
                </div>
            </div>
        </div>
    </div>
    
    <script>
        let progressInterval;
        
        function showDownloading() {
            const footer = document.getElementById('footer');
            footer.innerHTML = '<div>下载中请勿操作手机</div>';
            document.getElementById('modal').style.display = 'flex';
        }
        
        function showCompleted() {
            const footer = document.getElementById('footer');
            footer.innerHTML = '<button class="btn-install" onclick="alert(\'安装按钮点击成功！\')">立即安装</button>';
            document.getElementById('progressFill').style.width = '100%';
            document.getElementById('modal').style.display = 'flex';
        }
        
        function showError() {
            const footer = document.getElementById('footer');
            footer.innerHTML = '<div>下载失败，请重试</div>';
            document.getElementById('modal').style.display = 'flex';
        }
        
        function hideModal() {
            document.getElementById('modal').style.display = 'none';
            if (progressInterval) {
                clearInterval(progressInterval);
            }
        }
        
        function simulateProgress() {
            showDownloading();
            let progress = 0;
            const progressFill = document.getElementById('progressFill');
            
            progressInterval = setInterval(() => {
                progress += Math.random() * 10;
                if (progress >= 100) {
                    progress = 100;
                    clearInterval(progressInterval);
                    setTimeout(() => {
                        showCompleted();
                    }, 500);
                }
                progressFill.style.width = progress + '%';
            }, 200);
        }
        
        // 点击背景关闭弹窗
        document.getElementById('modal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideModal();
            }
        });
    </script>
</body>
</html>
