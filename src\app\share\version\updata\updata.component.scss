@import "../../../../theme/variables.scss";

.update-container {
  display: flex;
  flex-direction: column;
  height: 100%; // 使用容器高度而不是视口高度
  max-height: 70vh; // 限制最大高度为视口的70%
  min-height: 200px; // 设置最小高度
  overflow: hidden;
}

.check-update-header {
  text-align: center;
  min-height: 42px; // 减少最小高度，使其更紧凑
  // padding: 10px 16px; // 减少内边距
  // line-height: 1.3; // 调整行高
  width: 100%;
  color: var(--ion-color-primary-contrast);
  background-color: var(--ion-color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
  flex-shrink: 0; // 防止header被压缩
}

.check-update-context {
  display: flex;
  flex-direction: column;
  justify-content: flex-start; // 改为顶部对齐，避免内容被遮挡
  align-items: flex-start; // 左对齐
  flex: 1; // 使用flex: 1而不是固定高度
  white-space: pre-line;
  overflow-y: auto; // 确保内容可以滚动
  overflow-x: hidden; // 隐藏横向滚动
  padding: 16px; // 增加内边距
  line-height: 1.6; // 增加行高提高可读性
  font-size: 14px;
  color: #333;
  word-wrap: break-word; // 确保长文本换行
  word-break: break-word;
  min-height: 0; // 确保flex子元素可以正确收缩
  max-height: calc(70vh - 84px); // 减去头部和底部的高度
  -webkit-overflow-scrolling: touch; // iOS滚动优化
}

.check-update-footer {
  border-top: 1px solid #f6f6f6;
  min-height: 42px; // 减少最小高度，使其更紧凑
  height: 42px; // 固定高度确保按钮可见
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 0 16px; // 添加左右内边距
  background-color: #fff;
  flex-shrink: 0; // 防止footer被压缩
  position: relative; // 确保按钮层级正确
  z-index: 10; // 确保按钮在最上层
}

.btn-confirm {
  width: 100%; // 使用全宽度
  text-align: center;
  color: var(--ion-color-primary);
  padding: 12px 16px; // 增加按钮内边距，提供更好的点击区域
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
  min-height: 42px; // 确保按钮有足够的高度
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: rgba(var(--ion-color-primary-rgb), 0.1);
  }

  &:active {
    background-color: rgba(var(--ion-color-primary-rgb), 0.2);
  }
}

.btn-cancal {
  width: 49%;
  text-align: center;
  color: #666;
  padding: 8px 8px; // 减少按钮内边距，使其更紧凑
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
}

// 添加响应式适配
@media (max-height: 600px) {
  .update-container {
    max-height: 80vh; // 在小屏幕上使用更多空间
  }

  .check-update-header {
    min-height: 38px; // 进一步减少高度
    padding: 8px 16px;
    font-size: 15px;
  }

  .check-update-context {
    padding: 12px;
    font-size: 13px;
    max-height: calc(80vh - 76px); // 调整最大高度
  }

  .check-update-footer {
    min-height: 38px; // 进一步减少高度
    height: 38px;
    padding: 0 12px;
  }

  .btn-confirm {
    padding: 10px 12px;
    min-height: 38px;
  }
}

@media (max-height: 500px) {
  .update-container {
    max-height: 90vh; // 在极小屏幕上使用更多空间
  }

  .check-update-header {
    min-height: 36px; // 进一步减少高度
    padding: 6px 16px;
    font-size: 14px;
  }

  .check-update-context {
    padding: 8px;
    font-size: 12px;
    max-height: calc(90vh - 72px); // 调整最大高度
  }

  .check-update-footer {
    min-height: 36px; // 进一步减少高度
    height: 36px;
    padding: 0 8px;
  }

  .btn-confirm {
    padding: 8px 10px;
    min-height: 36px;
    font-size: 13px;
  }
}

// 特殊机型兼容性处理
@media (max-width: 320px) {
  .update-container {
    max-height: 95vh; // 在极小宽度设备上使用更多空间
  }

  .check-update-context {
    padding: 8px;
    font-size: 12px;
  }

  .btn-confirm {
    font-size: 12px;
    padding: 8px;
  }
}

// 确保在所有设备上都能正确显示
.update-container {
  // 防止内容溢出
  contain: layout style;
  // 确保在所有浏览器中正确渲染
  box-sizing: border-box;

  * {
    box-sizing: border-box;
  }
}

// 修复某些Android设备上的滚动问题
.check-update-context {
  // 强制启用硬件加速
  transform: translateZ(0);
  // 确保滚动条在需要时显示
  scrollbar-width: thin;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 2px;
  }
}
