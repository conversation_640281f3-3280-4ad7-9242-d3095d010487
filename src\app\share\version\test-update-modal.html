<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>更新弹窗测试</title>
    <style>
        /* 模拟 Ionic 模态框样式 */
        .modal-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        
        .dialog-box-xs {
            --height: auto;
            --max-height: 70vh;
            --min-height: 200px;
            --width: 280px;
            height: var(--height);
            max-height: var(--max-height);
            min-height: var(--min-height);
            width: var(--width);
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
        }
        
        /* 复制组件样式 */
        .update-container {
            display: flex;
            flex-direction: column;
            height: 100%;
            max-height: 70vh;
            min-height: 200px;
            overflow: hidden;
            box-sizing: border-box;
        }
        
        .check-update-header {
            text-align: center;
            min-height: 42px;
            width: 100%;
            color: white;
            background-color: #4285F4;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: 500;
            flex-shrink: 0;
        }
        
        .check-update-context {
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: flex-start;
            flex: 1;
            white-space: pre-line;
            overflow-y: auto;
            overflow-x: hidden;
            padding: 16px;
            line-height: 1.6;
            font-size: 14px;
            color: #333;
            word-wrap: break-word;
            word-break: break-word;
            min-height: 0;
            max-height: calc(70vh - 84px);
            -webkit-overflow-scrolling: touch;
            transform: translateZ(0);
            scrollbar-width: thin;
        }
        
        .check-update-context::-webkit-scrollbar {
            width: 4px;
        }
        
        .check-update-context::-webkit-scrollbar-track {
            background: transparent;
        }
        
        .check-update-context::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 2px;
        }
        
        .check-update-footer {
            border-top: 1px solid #f6f6f6;
            min-height: 42px;
            height: 42px;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            padding: 0 16px;
            background-color: #fff;
            flex-shrink: 0;
            position: relative;
            z-index: 10;
        }
        
        .btn-confirm {
            width: 100%;
            text-align: center;
            color: #4285F4;
            padding: 12px 16px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            border-radius: 4px;
            transition: background-color 0.2s;
            min-height: 42px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            background: transparent;
        }
        
        .btn-confirm:hover {
            background-color: rgba(66, 133, 244, 0.1);
        }
        
        .btn-confirm:active {
            background-color: rgba(66, 133, 244, 0.2);
        }
        
        .test-controls {
            position: fixed;
            top: 10px;
            left: 10px;
            z-index: 2000;
            background: white;
            padding: 10px;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .test-controls button {
            margin: 5px;
            padding: 8px 12px;
            border: 1px solid #ccc;
            border-radius: 4px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="test-controls">
        <button onclick="showShortContent()">短内容测试</button>
        <button onclick="showLongContent()">长内容测试</button>
        <button onclick="hideModal()">关闭弹窗</button>
    </div>
    
    <div id="modal" class="modal-backdrop" style="display: none;">
        <div class="dialog-box-xs">
            <div class="update-container">
                <div class="check-update-header">
                    发现新的版本
                </div>
                <div class="check-update-context" id="content">
                    <!-- 内容将通过JavaScript动态填充 -->
                </div>
                <div class="check-update-footer">
                    <button class="btn-confirm" onclick="alert('更新按钮点击成功！')">
                        马上更新
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function showShortContent() {
            const content = document.getElementById('content');
            content.textContent = '1. 修复了一些已知问题\n2. 优化了用户体验';
            document.getElementById('modal').style.display = 'flex';
        }
        
        function showLongContent() {
            const content = document.getElementById('content');
            content.textContent = `1. 实时监控新增倒计时功能
2. 关键点列表支持地图定位
3. 新采集关键点无法采用问题处理
4. 修复巡检任务中关键点状态异常问题
5. 修复部分机型深色模式显示异常问题
6. 优化了应用启动速度
7. 增强了数据同步稳定性
8. 改进了用户界面响应速度
9. 修复了网络连接异常时的崩溃问题
10. 添加了更多的错误提示信息`;
            document.getElementById('modal').style.display = 'flex';
        }
        
        function hideModal() {
            document.getElementById('modal').style.display = 'none';
        }
        
        // 点击背景关闭弹窗
        document.getElementById('modal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideModal();
            }
        });
    </script>
</body>
</html>
